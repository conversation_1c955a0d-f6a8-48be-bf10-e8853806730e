import { useState, useEffect } from 'react';
import { Home, Shirt, Calendar, User } from 'lucide-react';
import { useLocation, useNavigate } from 'react-router-dom';

interface TabBarProps {
  isVisible: boolean;
}

export const TabBar = ({ isVisible }: TabBarProps) => {
  const location = useLocation();
  const navigate = useNavigate();
  
  const tabs = [
    { id: 'home', icon: Home, label: 'Home', path: '/' },
    { id: 'closet', icon: Shirt, label: 'Closet', path: '/closet' },
    { id: 'schedule', icon: Calendar, label: 'Schedule', path: '/schedule' },
    { id: 'profile', icon: User, label: 'Profile', path: '/profile' }
  ];

  return (
    <div className={`
      fixed bottom-0 left-0 right-0 z-50
      glass-navigation
      transition-all duration-300 ease-out
      ${isVisible ? 'tab-bar-visible' : 'tab-bar-hidden'}
    `}>
      <div className="flex items-center justify-around px-6 py-3 safe-area-bottom">
        {tabs.map((tab) => {
          const Icon = tab.icon;
          const isActive = location.pathname === tab.path;
          
          return (
            <button
              key={tab.id}
              onClick={() => navigate(tab.path)}
              className={`
                flex flex-col items-center space-y-1 px-3 py-2 rounded-xl
                transition-all duration-200
                ${isActive 
                  ? 'text-primary scale-110' 
                  : 'text-muted-foreground hover:text-foreground hover:scale-105'
                }
              `}
            >
              <Icon size={20} strokeWidth={isActive ? 2 : 1.5} />
              <span className={`text-xs tracking-wide ${isActive ? 'font-medium' : 'font-light'}`}>
                {tab.label}
              </span>
            </button>
          );
        })}
      </div>
    </div>
  );
};