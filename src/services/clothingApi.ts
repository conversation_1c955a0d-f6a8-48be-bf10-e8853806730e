import { getApiConfig } from './backendApi';

export interface ClothingAnalysisResult {
  category: string;
  color: string;
}

export interface ClothingUploadResponse {
  success: boolean;
  data: {
    originalImage: string;
    processedImage: string;
    analysis: ClothingAnalysisResult;
  };
}

export interface ClothingItem {
  id: string;
  user_id: string;
  category_id?: string;
  name: string;
  color: string;
  brand?: string;
  size?: string;
  image_url?: string;
  tags?: string[];
  created_at: string;
  updated_at: string;
  category_name?: string;
}

export interface SaveClothingItemRequest {
  userId: string;
  name?: string;
  category: string;
  color: string;
  brand?: string;
  size?: string;
  season?: string;
  imageUrl?: string;
  processedImageUrl?: string;
}

export class ClothingApiService {
  private static getBaseUrl() {
    return getApiConfig().baseUrl;
  }

  /**
   * Upload and analyze a clothing image
   */
  static async uploadAndAnalyzeImage(file: File): Promise<ClothingUploadResponse> {
    try {
      const formData = new FormData();
      formData.append('image', file);

      const response = await fetch(`${this.getBaseUrl()}/clothing/upload`, {
        method: 'POST',
        body: formData,
        headers: {
          // Don't set Content-Type header - let browser set it with boundary for FormData
        },
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || `HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      return data;
    } catch (error) {
      console.error('Error uploading and analyzing image:', error);
      throw error;
    }
  }

  /**
   * Save a clothing item to the database
   */
  static async saveClothingItem(itemData: SaveClothingItemRequest): Promise<ClothingItem> {
    try {
      const response = await fetch(`${this.getBaseUrl()}/clothing`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(itemData),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || `HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      return data.data;
    } catch (error) {
      console.error('Error saving clothing item:', error);
      throw error;
    }
  }

  /**
   * Get all clothing items for a user
   */
  static async getClothingItems(userId: string): Promise<ClothingItem[]> {
    try {
      const response = await fetch(`${this.getBaseUrl()}/clothing/${userId}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || `HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      return data.data;
    } catch (error) {
      console.error('Error fetching clothing items:', error);
      throw error;
    }
  }

  /**
   * Delete a clothing item
   */
  static async deleteClothingItem(itemId: string): Promise<void> {
    try {
      const response = await fetch(`${this.getBaseUrl()}/clothing/${itemId}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || `HTTP error! status: ${response.status}`);
      }
    } catch (error) {
      console.error('Error deleting clothing item:', error);
      throw error;
    }
  }

  /**
   * Update a clothing item
   */
  static async updateClothingItem(itemId: string, updates: Partial<SaveClothingItemRequest>): Promise<ClothingItem> {
    try {
      const response = await fetch(`${this.getBaseUrl()}/clothing/${itemId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updates),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || `HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      return data.data;
    } catch (error) {
      console.error('Error updating clothing item:', error);
      throw error;
    }
  }

  /**
   * Get clothing categories
   */
  static async getCategories(): Promise<{ id: string; name: string; description?: string }[]> {
    try {
      const response = await fetch(`${this.getBaseUrl()}/clothing/categories`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || `HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      return data.data;
    } catch (error) {
      console.error('Error fetching categories:', error);
      throw error;
    }
  }
}
