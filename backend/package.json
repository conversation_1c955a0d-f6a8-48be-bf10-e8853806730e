{"name": "closet-glass-chic-backend", "version": "1.0.0", "description": "Backend services for Closet Glass Chic application", "main": "dist/index.js", "type": "module", "scripts": {"build": "tsc", "dev": "tsx watch src/index.ts", "start": "node dist/index.js", "test": "vitest", "test:coverage": "vitest --coverage", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "morgan": "^1.10.0", "dotenv": "^16.3.1", "pg": "^8.11.3", "node-fetch": "^3.3.2", "multer": "^1.4.5-lts.1", "sharp": "^0.32.6", "joi": "^17.11.0", "express-rate-limit": "^7.1.5", "uuid": "^9.0.1"}, "devDependencies": {"@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/morgan": "^1.9.9", "@types/multer": "^1.4.11", "@types/node": "^20.10.0", "@types/pg": "^8.10.9", "@types/supertest": "^2.0.16", "@types/uuid": "^9.0.7", "supertest": "^6.3.3", "tsx": "^4.6.0", "typescript": "^5.3.0", "vitest": "^1.0.0", "@vitest/coverage-v8": "^1.0.0", "eslint": "^8.55.0", "@typescript-eslint/eslint-plugin": "^6.13.0", "@typescript-eslint/parser": "^6.13.0"}, "keywords": ["fashion", "wardrobe", "api", "backend", "typescript"], "author": "Closet Glass Chic Team", "license": "MIT"}