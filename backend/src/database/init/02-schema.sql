-- Main database schema
-- This script creates all the required tables and indexes

-- Users Table (for authentication)
CREATE TABLE IF NOT EXISTS users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    email VARCHAR(255) UNIQUE NOT NULL CHECK (email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$'),
    password_hash VARCHAR(255) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    last_login_at TIMESTAMP WITH TIME ZONE,
    status user_status DEFAULT 'active'
);

-- User Profiles Table
CREATE TABLE IF NOT EXISTS user_profiles (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    first_name VARCHAR(50) NOT NULL CHECK (length(trim(first_name)) > 0),
    last_name VARCHAR(50) NOT NULL CHECK (length(trim(last_name)) > 0),
    gender gender_type NOT NULL,
    date_of_birth DATE NOT NULL CHECK (date_of_birth <= CURRENT_DATE AND date_of_birth >= '1900-01-01'),
    city_name VARCHAR(100) NOT NULL CHECK (length(trim(city_name)) > 0),
    latitude DECIMAL(10, 8) NOT NULL CHECK (latitude >= -90 AND latitude <= 90),
    longitude DECIMAL(11, 8) NOT NULL CHECK (longitude >= -180 AND longitude <= 180),
    weather_preferences JSONB DEFAULT '{}',
    status user_status DEFAULT 'active',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Clothing Categories Table
CREATE TABLE IF NOT EXISTS clothing_categories (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(50) NOT NULL UNIQUE CHECK (length(trim(name)) > 0),
    description TEXT,
    parent_category_id UUID REFERENCES clothing_categories(id) ON DELETE SET NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Clothing Items Table
CREATE TABLE IF NOT EXISTS clothing_items (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES user_profiles(id) ON DELETE CASCADE,
    category_id UUID REFERENCES clothing_categories(id) ON DELETE SET NULL,
    name VARCHAR(100) NOT NULL CHECK (length(trim(name)) > 0),
    description TEXT,
    color VARCHAR(50),
    brand VARCHAR(50),
    size VARCHAR(20),
    purchase_date DATE,
    purchase_price DECIMAL(10, 2) CHECK (purchase_price >= 0),
    image_url TEXT,
    image_metadata JSONB DEFAULT '{}',
    tags TEXT[] DEFAULT '{}',
    is_favorite BOOLEAN DEFAULT FALSE,
    wear_count INTEGER DEFAULT 0 CHECK (wear_count >= 0),
    last_worn_date DATE,
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'donated', 'sold', 'damaged', 'lost')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Outfits Table
CREATE TABLE IF NOT EXISTS outfits (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES user_profiles(id) ON DELETE CASCADE,
    name VARCHAR(100) NOT NULL CHECK (length(trim(name)) > 0),
    description TEXT,
    occasion VARCHAR(50),
    season VARCHAR(20) CHECK (season IN ('spring', 'summer', 'fall', 'winter', 'all')),
    weather_conditions JSONB DEFAULT '{}',
    is_favorite BOOLEAN DEFAULT FALSE,
    wear_count INTEGER DEFAULT 0 CHECK (wear_count >= 0),
    last_worn_date DATE,
    image_url TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Outfit Items Junction Table
CREATE TABLE IF NOT EXISTS outfit_items (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    outfit_id UUID NOT NULL REFERENCES outfits(id) ON DELETE CASCADE,
    clothing_item_id UUID NOT NULL REFERENCES clothing_items(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(outfit_id, clothing_item_id)
);

-- Weather Data Cache Table
CREATE TABLE IF NOT EXISTS weather_cache (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    latitude DECIMAL(10, 8) NOT NULL,
    longitude DECIMAL(11, 8) NOT NULL,
    weather_data JSONB NOT NULL,
    cached_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    UNIQUE(latitude, longitude)
);

-- User Sessions Table (for authentication)
CREATE TABLE IF NOT EXISTS user_sessions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    session_token VARCHAR(255) NOT NULL UNIQUE,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    last_accessed_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Log schema creation
INSERT INTO initialization_log (script_name, status) 
VALUES ('02-schema.sql', 'success');

-- Display schema creation message
DO $$
BEGIN
    RAISE NOTICE 'Database schema created successfully';
    RAISE NOTICE 'Tables created: users, user_profiles, clothing_categories, clothing_items, outfits, outfit_items, weather_cache, user_sessions';
END $$;
