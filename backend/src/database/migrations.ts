import { readFileSync } from 'fs';
import { join } from 'path';
import { DatabaseConnection } from './connection.js';
import { databaseLogger } from '../utils/logger.js';

// Migration interface
interface Migration {
  id: string;
  name: string;
  sql: string;
  rollbackSql?: string;
}

// Migration manager class
export class MigrationManager {
  private db: DatabaseConnection;

  constructor(db: DatabaseConnection) {
    this.db = db;
  }

  // Create migrations table if it doesn't exist
  async createMigrationsTable(): Promise<void> {
    const sql = `
      CREATE TABLE IF NOT EXISTS migrations (
        id VARCHAR(255) PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        executed_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        checksum VARCHAR(64) NOT NULL
      );
      
      CREATE INDEX IF NOT EXISTS idx_migrations_executed_at ON migrations(executed_at);
    `;

    try {
      await this.db.query(sql);
      databaseLogger.info('Migrations table created/verified');
    } catch (error) {
      databaseLogger.error('Failed to create migrations table', {}, error instanceof Error ? error : undefined);
      throw error;
    }
  }

  // Get list of executed migrations
  async getExecutedMigrations(): Promise<string[]> {
    try {
      const result = await this.db.query('SELECT id FROM migrations ORDER BY executed_at');
      return result.rows.map(row => row.id);
    } catch (error) {
      databaseLogger.error('Failed to get executed migrations', {}, error instanceof Error ? error : undefined);
      throw error;
    }
  }

  // Calculate checksum for migration content
  private calculateChecksum(content: string): string {
    const crypto = require('crypto');
    return crypto.createHash('sha256').update(content).digest('hex');
  }

  // Record migration execution
  async recordMigration(migration: Migration): Promise<void> {
    const checksum = this.calculateChecksum(migration.sql);
    
    try {
      await this.db.query(
        'INSERT INTO migrations (id, name, checksum) VALUES ($1, $2, $3)',
        [migration.id, migration.name, checksum]
      );
      
      databaseLogger.info('Migration recorded', {
        id: migration.id,
        name: migration.name,
        checksum
      });
    } catch (error) {
      databaseLogger.error('Failed to record migration', {
        id: migration.id,
        name: migration.name
      }, error instanceof Error ? error : undefined);
      throw error;
    }
  }

  // Execute a single migration
  async executeMigration(migration: Migration): Promise<void> {
    databaseLogger.info('Executing migration', {
      id: migration.id,
      name: migration.name
    });

    try {
      await this.db.transaction(async (client) => {
        // Execute the migration SQL
        await client.query(migration.sql);
        
        // Record the migration
        const checksum = this.calculateChecksum(migration.sql);
        await client.query(
          'INSERT INTO migrations (id, name, checksum) VALUES ($1, $2, $3)',
          [migration.id, migration.name, checksum]
        );
      });

      databaseLogger.info('Migration executed successfully', {
        id: migration.id,
        name: migration.name
      });
    } catch (error) {
      databaseLogger.error('Migration execution failed', {
        id: migration.id,
        name: migration.name
      }, error instanceof Error ? error : undefined);
      throw error;
    }
  }

  // Load migration from file
  loadMigrationFromFile(filePath: string): Migration {
    try {
      const content = readFileSync(filePath, 'utf8');
      const fileName = filePath.split('/').pop() || '';
      const id = fileName.replace('.sql', '');
      
      return {
        id,
        name: fileName,
        sql: content
      };
    } catch (error) {
      databaseLogger.error('Failed to load migration file', {
        filePath
      }, error instanceof Error ? error : undefined);
      throw error;
    }
  }

  // Run all pending migrations
  async runMigrations(migrationsDir: string): Promise<void> {
    databaseLogger.info('Starting database migrations');

    try {
      // Create migrations table
      await this.createMigrationsTable();

      // Get executed migrations
      const executedMigrations = await this.getExecutedMigrations();
      databaseLogger.info('Found executed migrations', { count: executedMigrations.length });

      // Load schema migration
      const schemaPath = join(migrationsDir, 'schema.sql');
      const schemaMigration = this.loadMigrationFromFile(schemaPath);

      // Check if schema migration needs to be run
      if (!executedMigrations.includes(schemaMigration.id)) {
        databaseLogger.info('Running schema migration');
        await this.executeMigration(schemaMigration);
      } else {
        databaseLogger.info('Schema migration already executed');
      }

      databaseLogger.info('All migrations completed successfully');
    } catch (error) {
      databaseLogger.error('Migration process failed', {}, error instanceof Error ? error : undefined);
      throw error;
    }
  }

  // Verify database schema
  async verifySchema(): Promise<{ valid: boolean; issues: string[] }> {
    const issues: string[] = [];

    try {
      databaseLogger.info('Verifying database schema');

      // Check required tables
      const requiredTables = [
        'user_profiles',
        'clothing_categories',
        'clothing_items',
        'outfits',
        'outfit_items',
        'weather_cache',
        'user_sessions'
      ];

      for (const table of requiredTables) {
        const result = await this.db.query(
          `SELECT EXISTS (
            SELECT FROM information_schema.tables 
            WHERE table_schema = 'public' 
            AND table_name = $1
          )`,
          [table]
        );

        if (!result.rows[0].exists) {
          issues.push(`Missing required table: ${table}`);
        }
      }

      // Check required extensions
      const result = await this.db.query(
        `SELECT EXISTS (
          SELECT FROM pg_extension 
          WHERE extname = 'uuid-ossp'
        )`
      );

      if (!result.rows[0].exists) {
        issues.push('Missing required extension: uuid-ossp');
      }

      // Check required enums
      const enumResult = await this.db.query(
        `SELECT typname FROM pg_type 
         WHERE typtype = 'e' 
         AND typname IN ('gender_type', 'user_status')`
      );

      const existingEnums = enumResult.rows.map(row => row.typname);
      if (!existingEnums.includes('gender_type')) {
        issues.push('Missing required enum: gender_type');
      }
      if (!existingEnums.includes('user_status')) {
        issues.push('Missing required enum: user_status');
      }

      const isValid = issues.length === 0;
      
      if (isValid) {
        databaseLogger.info('Database schema verification passed');
      } else {
        databaseLogger.warn('Database schema verification failed', { issues });
      }

      return { valid: isValid, issues };
    } catch (error) {
      databaseLogger.error('Schema verification failed', {}, error instanceof Error ? error : undefined);
      return { 
        valid: false, 
        issues: [`Schema verification error: ${error instanceof Error ? error.message : 'Unknown error'}`] 
      };
    }
  }

  // Get database statistics
  async getDatabaseStats(): Promise<any> {
    try {
      const stats = await this.db.query(`
        SELECT 
          schemaname,
          tablename,
          n_tup_ins as inserts,
          n_tup_upd as updates,
          n_tup_del as deletes,
          n_live_tup as live_tuples,
          n_dead_tup as dead_tuples
        FROM pg_stat_user_tables
        ORDER BY tablename
      `);

      const indexStats = await this.db.query(`
        SELECT 
          schemaname,
          tablename,
          indexname,
          idx_scan as scans,
          idx_tup_read as tuples_read,
          idx_tup_fetch as tuples_fetched
        FROM pg_stat_user_indexes
        ORDER BY tablename, indexname
      `);

      return {
        tables: stats.rows,
        indexes: indexStats.rows,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      databaseLogger.error('Failed to get database stats', {}, error instanceof Error ? error : undefined);
      throw error;
    }
  }
}

// Initialize and run migrations
export async function initializeDatabaseSchema(db: DatabaseConnection): Promise<void> {
  const migrationManager = new MigrationManager(db);
  const migrationsDir = join(process.cwd(), 'src', 'database');
  
  await migrationManager.runMigrations(migrationsDir);
  
  // Verify schema after migrations
  const verification = await migrationManager.verifySchema();
  if (!verification.valid) {
    throw new Error(`Schema verification failed: ${verification.issues.join(', ')}`);
  }
}
