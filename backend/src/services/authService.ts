import { DatabaseConnection } from '../database/connection.js';
import { databaseLogger } from '../utils/logger.js';
import crypto from 'crypto';

// Authentication interfaces
export interface User {
  id: string;
  email: string;
  createdAt: string;
  updatedAt: string;
  lastLoginAt?: string;
  status: 'active' | 'inactive' | 'suspended';
}

export interface AuthCredentials {
  email: string;
  password: string;
}

export interface AuthResult {
  success: boolean;
  user?: User;
  error?: string;
}

export interface CreateUserData {
  email: string;
  password: string;
}

export interface PasswordValidationResult {
  isValid: boolean;
  errors: string[];
}

// Authentication service class
export class AuthService {
  private db: DatabaseConnection;

  constructor(db: DatabaseConnection) {
    this.db = db;
  }

  /**
   * Hash a password using crypto
   */
  private async hashPassword(password: string): Promise<string> {
    return new Promise((resolve, reject) => {
      const salt = crypto.randomBytes(16).toString('hex');
      crypto.pbkdf2(password, salt, 10000, 64, 'sha512', (err, derivedKey) => {
        if (err) reject(err);
        resolve(salt + ':' + derivedKey.toString('hex'));
      });
    });
  }

  /**
   * Verify a password against a hash
   */
  private async verifyPassword(password: string, hash: string): Promise<boolean> {
    return new Promise((resolve, reject) => {
      const [salt, key] = hash.split(':');
      crypto.pbkdf2(password, salt, 10000, 64, 'sha512', (err, derivedKey) => {
        if (err) reject(err);
        resolve(key === derivedKey.toString('hex'));
      });
    });
  }

  /**
   * Validate email format
   */
  private validateEmail(email: string): boolean {
    const emailRegex = /^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$/;
    return emailRegex.test(email);
  }

  /**
   * Validate password strength
   */
  private validatePassword(password: string): PasswordValidationResult {
    const errors: string[] = [];

    if (password.length < 8) {
      errors.push('Password must be at least 8 characters long');
    }

    if (!/[A-Z]/.test(password)) {
      errors.push('Password must contain at least one uppercase letter');
    }

    if (!/[a-z]/.test(password)) {
      errors.push('Password must contain at least one lowercase letter');
    }

    if (!/\d/.test(password)) {
      errors.push('Password must contain at least one number');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Register a new user
   */
  async register(credentials: AuthCredentials, requestId?: string): Promise<AuthResult> {
    try {
      databaseLogger.debug('Starting user registration', { email: credentials.email }, requestId);

      // Validate email
      if (!this.validateEmail(credentials.email)) {
        return {
          success: false,
          error: 'Invalid email format'
        };
      }

      // Validate password
      const passwordValidation = this.validatePassword(credentials.password);
      if (!passwordValidation.isValid) {
        return {
          success: false,
          error: passwordValidation.errors.join(', ')
        };
      }

      // Check if user already exists
      const existingUser = await this.getUserByEmail(credentials.email, requestId);
      if (existingUser) {
        return {
          success: false,
          error: 'User with this email already exists'
        };
      }

      // Hash password
      const passwordHash = await this.hashPassword(credentials.password);

      // Create user
      const result = await this.db.query<User>(
        `INSERT INTO users (email, password_hash) 
         VALUES ($1, $2) 
         RETURNING id, email, created_at as "createdAt", updated_at as "updatedAt", status`,
        [credentials.email, passwordHash],
        requestId
      );

      const user = result.rows[0];
      databaseLogger.info('User registered successfully', { userId: user.id, email: user.email }, requestId);

      return {
        success: true,
        user
      };

    } catch (error) {
      databaseLogger.error('User registration failed', {
        email: credentials.email,
        error: error instanceof Error ? error.message : 'Unknown error'
      }, error instanceof Error ? error : undefined, requestId);

      return {
        success: false,
        error: 'Registration failed. Please try again.'
      };
    }
  }

  /**
   * Sign in an existing user
   */
  async signIn(credentials: AuthCredentials, requestId?: string): Promise<AuthResult> {
    try {
      databaseLogger.debug('Starting user sign in', { email: credentials.email }, requestId);

      // Validate email format
      if (!this.validateEmail(credentials.email)) {
        return {
          success: false,
          error: 'Invalid email format'
        };
      }

      // Get user by email
      const user = await this.getUserByEmail(credentials.email, requestId);
      if (!user) {
        return {
          success: false,
          error: 'Invalid email or password'
        };
      }

      // Get password hash
      const passwordResult = await this.db.query(
        'SELECT password_hash FROM users WHERE id = $1',
        [user.id],
        requestId
      );

      if (passwordResult.rows.length === 0) {
        return {
          success: false,
          error: 'Invalid email or password'
        };
      }

      // Verify password
      const isValidPassword = await this.verifyPassword(credentials.password, passwordResult.rows[0].password_hash);
      if (!isValidPassword) {
        return {
          success: false,
          error: 'Invalid email or password'
        };
      }

      // Update last login
      await this.db.query(
        'UPDATE users SET last_login_at = CURRENT_TIMESTAMP WHERE id = $1',
        [user.id],
        requestId
      );

      databaseLogger.info('User signed in successfully', { userId: user.id, email: user.email }, requestId);

      return {
        success: true,
        user: {
          ...user,
          lastLoginAt: new Date().toISOString()
        }
      };

    } catch (error) {
      databaseLogger.error('User sign in failed', {
        email: credentials.email,
        error: error instanceof Error ? error.message : 'Unknown error'
      }, error instanceof Error ? error : undefined, requestId);

      return {
        success: false,
        error: 'Sign in failed. Please try again.'
      };
    }
  }

  /**
   * Get user by email
   */
  async getUserByEmail(email: string, requestId?: string): Promise<User | null> {
    try {
      const result = await this.db.query<User>(
        `SELECT id, email, created_at as "createdAt", updated_at as "updatedAt", 
                last_login_at as "lastLoginAt", status 
         FROM users 
         WHERE email = $1 AND status = 'active'`,
        [email],
        requestId
      );

      return result.rows.length > 0 ? result.rows[0] : null;
    } catch (error) {
      databaseLogger.error('Failed to get user by email', {
        email,
        error: error instanceof Error ? error.message : 'Unknown error'
      }, error instanceof Error ? error : undefined, requestId);
      
      return null;
    }
  }

  /**
   * Get user by ID
   */
  async getUserById(id: string, requestId?: string): Promise<User | null> {
    try {
      const result = await this.db.query<User>(
        `SELECT id, email, created_at as "createdAt", updated_at as "updatedAt", 
                last_login_at as "lastLoginAt", status 
         FROM users 
         WHERE id = $1 AND status = 'active'`,
        [id],
        requestId
      );

      return result.rows.length > 0 ? result.rows[0] : null;
    } catch (error) {
      databaseLogger.error('Failed to get user by ID', {
        id,
        error: error instanceof Error ? error.message : 'Unknown error'
      }, error instanceof Error ? error : undefined, requestId);
      
      return null;
    }
  }

  /**
   * Validate email availability for registration with enhanced error handling
   */
  async validateEmailAvailability(email: string, requestId?: string): Promise<{
    available: boolean;
    message: string;
  }> {
    try {
      databaseLogger.debug('Validating email availability', { email }, requestId);

      // Validate email format first
      if (!this.validateEmail(email)) {
        databaseLogger.debug('Email format validation failed', { email }, requestId);
        return {
          available: false,
          message: 'Invalid email format'
        };
      }

      // Add timeout handling for database operations (requirement 3.3)
      const timeoutPromise = new Promise<never>((_, reject) => {
        setTimeout(() => {
          reject(new Error('Database query timeout'));
        }, 3000); // 3 second timeout for database operations
      });

      // Race between database query and timeout
      const existingUser = await Promise.race([
        this.getUserByEmail(email, requestId),
        timeoutPromise
      ]);
      
      if (existingUser) {
        databaseLogger.debug('Email already exists in database', { email }, requestId);
        return {
          available: false,
          message: 'Email is already registered'
        };
      }

      databaseLogger.debug('Email is available for registration', { email }, requestId);
      return {
        available: true,
        message: 'Email is available'
      };

    } catch (error) {
      // Enhanced error handling with specific error types
      if (error instanceof Error) {
        if (error.message === 'Database query timeout') {
          databaseLogger.warn('Email validation timed out at database level', { email }, requestId);
          // Per requirement 3.3: treat timeout as allowing signup to continue
          return {
            available: true,
            message: 'Validation timed out. You may proceed with signup.'
          };
        }
        
        // Handle specific database connection errors
        if (error.message.includes('connection') || error.message.includes('ECONNREFUSED')) {
          databaseLogger.error('Database connection error during email validation', {
            email,
            error: error.message
          }, error, requestId);
          return {
            available: true,
            message: 'Database temporarily unavailable. You may proceed with signup.'
          };
        }
      }

      databaseLogger.error('Email availability validation failed', {
        email,
        error: error instanceof Error ? error.message : 'Unknown error'
      }, error instanceof Error ? error : undefined, requestId);

      // Return generic error message for security, but allow signup to continue (requirement 3.4)
      return {
        available: true,
        message: 'Unable to verify email availability. You may proceed with signup.'
      };
    }
  }
}
