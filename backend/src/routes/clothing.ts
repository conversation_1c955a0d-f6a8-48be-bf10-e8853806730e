import express from 'express';
import multer from 'multer';
import path from 'path';
import fs from 'fs/promises';
import { spawn } from 'child_process';
import sharp from 'sharp';
import { v4 as uuidv4 } from 'uuid';
import { logger } from '../utils/logger.js';
import { initializeDatabase } from '../database/connection.js';

const router = express.Router();

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: async (req, file, cb) => {
    const uploadDir = path.join(process.cwd(), 'uploads', 'clothing');
    try {
      await fs.mkdir(uploadDir, { recursive: true });
      cb(null, uploadDir);
    } catch (error) {
      cb(error as Error, '');
    }
  },
  filename: (req, file, cb) => {
    const uniqueName = `${uuidv4()}-${Date.now()}${path.extname(file.originalname)}`;
    cb(null, uniqueName);
  }
});

const upload = multer({
  storage,
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB limit
  },
  fileFilter: (req, file, cb) => {
    const allowedTypes = ['image/jpeg', 'image/png', 'image/webp'];
    if (allowedTypes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new Error('Invalid file type. Only JPEG, PNG, and WebP are allowed.'));
    }
  }
});

// Helper function to run Python clothing analysis
const runClothingAnalysis = (inputPath: string, outputDir: string): Promise<any> => {
  return new Promise((resolve, reject) => {
    const pythonScript = path.join(process.cwd(), 'python', 'clothing_analyzer.py');
    const pythonProcess = spawn('python3', [pythonScript, inputPath, outputDir]);

    let stdout = '';
    let stderr = '';

    pythonProcess.stdout.on('data', (data) => {
      stdout += data.toString();
    });

    pythonProcess.stderr.on('data', (data) => {
      stderr += data.toString();
    });

    pythonProcess.on('close', (code) => {
      if (code === 0) {
        try {
          const result = JSON.parse(stdout);
          resolve(result);
        } catch (error) {
          reject(new Error(`Failed to parse Python output: ${error}`));
        }
      } else {
        reject(new Error(`Python script failed with code ${code}: ${stderr}`));
      }
    });

    pythonProcess.on('error', (error) => {
      reject(new Error(`Failed to start Python process: ${error.message}`));
    });
  });
};

// POST /api/clothing/upload - Upload and analyze clothing image
router.post('/upload', upload.single('image'), async (req, res) => {
  const requestId = req.headers['x-request-id'] as string;
  
  try {
    if (!req.file) {
      return res.status(400).json({
        error: 'No image file provided',
        timestamp: new Date().toISOString()
      });
    }

    logger.info('CLOTHING_UPLOAD', 'Processing clothing image upload', {
      requestId,
      filename: req.file.filename,
      size: req.file.size
    });

    // Optimize image with Sharp
    const optimizedPath = path.join(
      path.dirname(req.file.path),
      `optimized-${req.file.filename}`
    );

    await sharp(req.file.path)
      .resize(800, 1200, { 
        fit: 'inside',
        withoutEnlargement: true 
      })
      .jpeg({ quality: 85 })
      .toFile(optimizedPath);

    // Create output directory for processed images
    const outputDir = path.join(process.cwd(), 'uploads', 'processed');
    await fs.mkdir(outputDir, { recursive: true });

    // Run Python analysis
    const analysisResult = await runClothingAnalysis(optimizedPath, outputDir);

    if (!analysisResult.success) {
      logger.error('CLOTHING_ANALYSIS', 'Failed to analyze clothing', {
        requestId,
        error: analysisResult.error
      });
      
      return res.status(500).json({
        error: 'Failed to analyze clothing image',
        details: analysisResult.error,
        timestamp: new Date().toISOString()
      });
    }

    // Generate URLs for the images
    const originalUrl = `/uploads/clothing/${req.file.filename}`;
    const processedUrl = `/uploads/processed/${path.basename(analysisResult.processed_image_path)}`;

    logger.info('CLOTHING_ANALYSIS', 'Successfully analyzed clothing', {
      requestId,
      category: analysisResult.analysis.category,
      color: analysisResult.analysis.color
    });

    res.json({
      success: true,
      data: {
        originalImage: originalUrl,
        processedImage: processedUrl,
        analysis: analysisResult.analysis
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    logger.error('CLOTHING_UPLOAD', 'Error processing clothing upload', {
      requestId,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, error instanceof Error ? error : undefined);

    res.status(500).json({
      error: 'Internal server error during image processing',
      timestamp: new Date().toISOString()
    });
  }
});

// POST /api/clothing - Save clothing item to database
router.post('/', async (req, res) => {
  const requestId = req.headers['x-request-id'] as string;
  
  try {
    const {
      userId,
      name,
      category,
      color,
      brand,
      size,
      season,
      imageUrl,
      processedImageUrl
    } = req.body;

    // Validate required fields
    if (!userId || !category || !color) {
      return res.status(400).json({
        error: 'Missing required fields: userId, category, color',
        timestamp: new Date().toISOString()
      });
    }

    const db = await initializeDatabase();

    // Get category ID
    const categoryResult = await db.query(
      'SELECT id FROM clothing_categories WHERE name = $1',
      [category]
    );

    let categoryId = null;
    if (categoryResult.rows.length > 0) {
      categoryId = categoryResult.rows[0].id;
    }

    // Insert clothing item
    const result = await db.query(`
      INSERT INTO clothing_items (
        user_id, category_id, name, color, brand, size, 
        image_url, tags, created_at, updated_at
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
      RETURNING *
    `, [
      userId,
      categoryId,
      name || `${brand || 'Unknown'} ${category}`,
      color,
      brand,
      size,
      processedImageUrl || imageUrl,
      [season].filter(Boolean) // Add season as tag if provided
    ]);

    const clothingItem = result.rows[0];

    logger.info('CLOTHING_SAVE', 'Successfully saved clothing item', {
      requestId,
      clothingItemId: clothingItem.id,
      category,
      color
    });

    res.status(201).json({
      success: true,
      data: clothingItem,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    logger.error('CLOTHING_SAVE', 'Error saving clothing item', {
      requestId,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, error instanceof Error ? error : undefined);

    res.status(500).json({
      error: 'Internal server error while saving clothing item',
      timestamp: new Date().toISOString()
    });
  }
});

// GET /api/clothing/:userId - Get all clothing items for a user
router.get('/:userId', async (req, res) => {
  const requestId = req.headers['x-request-id'] as string;
  const { userId } = req.params;
  
  try {
    const db = await initializeDatabase();

    const result = await db.query(`
      SELECT 
        ci.*,
        cc.name as category_name
      FROM clothing_items ci
      LEFT JOIN clothing_categories cc ON ci.category_id = cc.id
      WHERE ci.user_id = $1 AND ci.status = 'active'
      ORDER BY ci.created_at DESC
    `, [userId]);

    logger.info('CLOTHING_GET', 'Retrieved clothing items', {
      requestId,
      userId,
      count: result.rows.length
    });

    res.json({
      success: true,
      data: result.rows,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    logger.error('CLOTHING_GET', 'Error retrieving clothing items', {
      requestId,
      userId,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, error instanceof Error ? error : undefined);

    res.status(500).json({
      error: 'Internal server error while retrieving clothing items',
      timestamp: new Date().toISOString()
    });
  }
});

export { router as clothingRouter };
