import { Request, Response, NextFunction } from 'express';
import <PERSON><PERSON> from 'joi';

// Validation schemas
const coordinatesSchema = Joi.object({
  lat: Joi.number().min(-90).max(90).required(),
  lon: Joi.number().min(-180).max(180).required()
});

const cityQuerySchema = Joi.object({
  q: Joi.string().min(2).max(100).required(),
  limit: Joi.number().min(1).max(50).optional()
});

const userProfileSchema = Joi.object({
  userId: Joi.string().uuid().required(),
  firstName: Joi.string().min(1).max(50).required(),
  lastName: Joi.string().min(1).max(50).required(),
  gender: Joi.string().valid('Male', 'Female', 'Other', 'Prefer not to say').required(),
  dateOfBirth: Joi.date().max('now').required(),
  cityName: Joi.string().min(2).max(100).required(),
  latitude: Joi.number().min(-90).max(90).required(),
  longitude: Joi.number().min(-180).max(180).required(),
  weatherPreferences: Joi.object().optional()
});

// Validation middleware functions
export const validateCoordinates = (req: Request, res: Response, next: NextFunction) => {
  const { error } = coordinatesSchema.validate(req.query);
  
  if (error) {
    return res.status(400).json({
      success: false,
      error: 'Invalid coordinates',
      details: error.details.map(detail => detail.message),
      timestamp: new Date().toISOString()
    });
  }
  
  next();
};

export const validateCityQuery = (req: Request, res: Response, next: NextFunction) => {
  const { error } = cityQuerySchema.validate(req.query);
  
  if (error) {
    return res.status(400).json({
      success: false,
      error: 'Invalid search query',
      details: error.details.map(detail => detail.message),
      timestamp: new Date().toISOString()
    });
  }
  
  next();
};

export const validateUserProfile = (req: Request, res: Response, next: NextFunction) => {
  const { error } = userProfileSchema.validate(req.body);
  
  if (error) {
    return res.status(400).json({
      success: false,
      error: 'Invalid user profile data',
      details: error.details.map(detail => detail.message),
      timestamp: new Date().toISOString()
    });
  }
  
  next();
};

// File upload validation
export const validateFileUpload = (req: Request, res: Response, next: NextFunction) => {
  if (!req.file) {
    return res.status(400).json({
      success: false,
      error: 'No file uploaded',
      timestamp: new Date().toISOString()
    });
  }

  const allowedTypes = ['image/jpeg', 'image/png', 'image/webp', 'image/heic'];
  const maxSize = 5 * 1024 * 1024; // 5MB

  if (!allowedTypes.includes(req.file.mimetype)) {
    return res.status(400).json({
      success: false,
      error: 'Invalid file type. Only JPEG, PNG, WebP, and HEIC images are allowed.',
      timestamp: new Date().toISOString()
    });
  }

  if (req.file.size > maxSize) {
    return res.status(400).json({
      success: false,
      error: 'File too large. Maximum size is 5MB.',
      timestamp: new Date().toISOString()
    });
  }

  next();
};
