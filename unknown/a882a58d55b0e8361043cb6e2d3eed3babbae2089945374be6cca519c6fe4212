-- Closet Glass Chic Database Schema
-- PostgreSQL Database Schema for User Profiles and Application Data

-- Enable UUID extension for generating UUIDs
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create enum types for better data integrity (skip if already exist)
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'gender_type') THEN
        CREATE TYPE gender_type AS ENUM ('Male', 'Female', 'Other', 'Prefer not to say');
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'user_status') THEN
        CREATE TYPE user_status AS ENUM ('active', 'inactive', 'suspended');
    END IF;
END $$;

-- User Profiles Table
CREATE TABLE user_profiles (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    first_name VARCHAR(50) NOT NULL CHECK (length(trim(first_name)) > 0),
    last_name VA<PERSON>HA<PERSON>(50) NOT NULL CHECK (length(trim(last_name)) > 0),
    gender gender_type NOT NULL,
    date_of_birth DATE NOT NULL CHECK (date_of_birth <= CURRENT_DATE AND date_of_birth >= '1900-01-01'),
    city_name VARCHAR(100) NOT NULL CHECK (length(trim(city_name)) > 0),
    latitude DECIMAL(10, 8) NOT NULL CHECK (latitude >= -90 AND latitude <= 90),
    longitude DECIMAL(11, 8) NOT NULL CHECK (longitude >= -180 AND longitude <= 180),
    weather_preferences JSONB DEFAULT '{}',
    status user_status DEFAULT 'active',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Clothing Categories Table
CREATE TABLE clothing_categories (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(50) NOT NULL UNIQUE CHECK (length(trim(name)) > 0),
    description TEXT,
    parent_category_id UUID REFERENCES clothing_categories(id) ON DELETE SET NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Clothing Items Table
CREATE TABLE clothing_items (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES user_profiles(id) ON DELETE CASCADE,
    category_id UUID REFERENCES clothing_categories(id) ON DELETE SET NULL,
    name VARCHAR(100) NOT NULL CHECK (length(trim(name)) > 0),
    description TEXT,
    color VARCHAR(50),
    brand VARCHAR(50),
    size VARCHAR(20),
    purchase_date DATE,
    purchase_price DECIMAL(10, 2) CHECK (purchase_price >= 0),
    image_url TEXT,
    image_metadata JSONB DEFAULT '{}',
    tags TEXT[] DEFAULT '{}',
    is_favorite BOOLEAN DEFAULT FALSE,
    wear_count INTEGER DEFAULT 0 CHECK (wear_count >= 0),
    last_worn_date DATE,
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'donated', 'sold', 'damaged', 'lost')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Outfits Table
CREATE TABLE outfits (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES user_profiles(id) ON DELETE CASCADE,
    name VARCHAR(100) NOT NULL CHECK (length(trim(name)) > 0),
    description TEXT,
    occasion VARCHAR(50),
    season VARCHAR(20) CHECK (season IN ('spring', 'summer', 'fall', 'winter', 'all')),
    weather_conditions JSONB DEFAULT '{}',
    is_favorite BOOLEAN DEFAULT FALSE,
    wear_count INTEGER DEFAULT 0 CHECK (wear_count >= 0),
    last_worn_date DATE,
    image_url TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Outfit Items Junction Table
CREATE TABLE outfit_items (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    outfit_id UUID NOT NULL REFERENCES outfits(id) ON DELETE CASCADE,
    clothing_item_id UUID NOT NULL REFERENCES clothing_items(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(outfit_id, clothing_item_id)
);

-- Weather Data Cache Table
CREATE TABLE weather_cache (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    latitude DECIMAL(10, 8) NOT NULL,
    longitude DECIMAL(11, 8) NOT NULL,
    weather_data JSONB NOT NULL,
    cached_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    UNIQUE(latitude, longitude)
);

-- User Sessions Table (for future authentication)
CREATE TABLE user_sessions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES user_profiles(id) ON DELETE CASCADE,
    session_token VARCHAR(255) NOT NULL UNIQUE,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    last_accessed_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for better performance
CREATE INDEX idx_user_profiles_email ON user_profiles(first_name, last_name);
CREATE INDEX idx_user_profiles_location ON user_profiles(latitude, longitude);
CREATE INDEX idx_user_profiles_created_at ON user_profiles(created_at);
CREATE INDEX idx_user_profiles_status ON user_profiles(status);

CREATE INDEX idx_clothing_items_user_id ON clothing_items(user_id);
CREATE INDEX idx_clothing_items_category_id ON clothing_items(category_id);
CREATE INDEX idx_clothing_items_status ON clothing_items(status);
CREATE INDEX idx_clothing_items_tags ON clothing_items USING GIN(tags);
CREATE INDEX idx_clothing_items_created_at ON clothing_items(created_at);

CREATE INDEX idx_outfits_user_id ON outfits(user_id);
CREATE INDEX idx_outfits_season ON outfits(season);
CREATE INDEX idx_outfits_created_at ON outfits(created_at);

CREATE INDEX idx_outfit_items_outfit_id ON outfit_items(outfit_id);
CREATE INDEX idx_outfit_items_clothing_item_id ON outfit_items(clothing_item_id);

CREATE INDEX idx_weather_cache_location ON weather_cache(latitude, longitude);
CREATE INDEX idx_weather_cache_expires_at ON weather_cache(expires_at);

CREATE INDEX idx_user_sessions_user_id ON user_sessions(user_id);
CREATE INDEX idx_user_sessions_token ON user_sessions(session_token);
CREATE INDEX idx_user_sessions_expires_at ON user_sessions(expires_at);

-- Create function to automatically update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers to automatically update updated_at
CREATE TRIGGER update_user_profiles_updated_at BEFORE UPDATE ON user_profiles FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_clothing_categories_updated_at BEFORE UPDATE ON clothing_categories FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_clothing_items_updated_at BEFORE UPDATE ON clothing_items FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_outfits_updated_at BEFORE UPDATE ON outfits FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Insert default clothing categories
INSERT INTO clothing_categories (name, description) VALUES
('Tops', 'Upper body clothing items'),
('Bottoms', 'Lower body clothing items'),
('Dresses', 'One-piece garments'),
('Outerwear', 'Jackets, coats, and outer garments'),
('Shoes', 'Footwear'),
('Accessories', 'Bags, jewelry, and other accessories'),
('Undergarments', 'Underwear and intimate apparel'),
('Activewear', 'Sports and exercise clothing'),
('Sleepwear', 'Pajamas and nightwear'),
('Formal', 'Formal and business attire');

-- Insert subcategories
INSERT INTO clothing_categories (name, description, parent_category_id) VALUES
('T-Shirts', 'Casual t-shirts and tees', (SELECT id FROM clothing_categories WHERE name = 'Tops')),
('Blouses', 'Dress shirts and blouses', (SELECT id FROM clothing_categories WHERE name = 'Tops')),
('Sweaters', 'Knitwear and sweaters', (SELECT id FROM clothing_categories WHERE name = 'Tops')),
('Jeans', 'Denim pants', (SELECT id FROM clothing_categories WHERE name = 'Bottoms')),
('Trousers', 'Dress pants and trousers', (SELECT id FROM clothing_categories WHERE name = 'Bottoms')),
('Skirts', 'Skirts of all lengths', (SELECT id FROM clothing_categories WHERE name = 'Bottoms')),
('Sneakers', 'Athletic and casual sneakers', (SELECT id FROM clothing_categories WHERE name = 'Shoes')),
('Heels', 'High heels and dress shoes', (SELECT id FROM clothing_categories WHERE name = 'Shoes')),
('Boots', 'Boots of all styles', (SELECT id FROM clothing_categories WHERE name = 'Shoes'));

-- Create view for user profile summary
CREATE VIEW user_profile_summary AS
SELECT 
    up.id,
    up.first_name,
    up.last_name,
    up.city_name,
    up.status,
    up.created_at,
    COUNT(ci.id) as total_clothing_items,
    COUNT(o.id) as total_outfits
FROM user_profiles up
LEFT JOIN clothing_items ci ON up.id = ci.user_id AND ci.status = 'active'
LEFT JOIN outfits o ON up.id = o.user_id
GROUP BY up.id, up.first_name, up.last_name, up.city_name, up.status, up.created_at;
